'use client';

import { motion } from 'framer-motion';
import { Check, Star, Zap, Crown, Building } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';

const Pricing = () => {
  const plans = [
    {
      name: 'Free',
      icon: Star,
      price: '0',
      period: 'Forever',
      description: 'Perfect for individual citizens',
      color: 'from-gray-400 to-gray-600',
      popular: false,
      features: [
        'Basic offense reporting',
        'Photo evidence upload',
        'Email notifications',
        'Community safety alerts',
        'Standard support',
        'Report history (30 days)'
      ],
      limitations: [
        'Limited to 5 reports/month',
        'Basic categorization only',
        'Standard processing time'
      ]
    },
    {
      name: 'Premium',
      icon: Zap,
      price: '2.99',
      period: 'per month',
      description: 'Enhanced features for active reporters',
      color: 'from-primary-blue to-primary-purple',
      popular: true,
      features: [
        'Unlimited offense reporting',
        'Video & audio evidence upload',
        'Priority report processing',
        'Real-time GPS tracking',
        'Advanced analytics dashboard',
        'Multiple report templates',
        'SMS + Push notifications',
        'Report history (unlimited)',
        'Priority support',
        'Custom report categories'
      ],
      limitations: []
    },
    {
      name: 'Enterprise',
      icon: Building,
      price: '299',
      period: 'per month',
      description: 'For organizations and departments',
      color: 'from-accent-orange to-accent-pink',
      popular: false,
      features: [
        'Everything in Premium',
        'Department dashboard',
        'Advanced analytics & reporting',
        'Custom workflows',
        'API access & integrations',
        'White-label options',
        'Dedicated account manager',
        'Custom training sessions',
        'SLA guarantees',
        'Multi-jurisdiction support',
        'Bulk user management',
        'Advanced security features'
      ],
      limitations: []
    }
  ];

  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    initial: { opacity: 0, y: 30, scale: 0.9 },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { duration: 0.6, ease: "easeOut" }
    },
    hover: {
      y: -10,
      scale: 1.02,
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };

  return (
    <section className="relative py-20 lg:py-32 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-dark-surface via-dark-bg to-dark-surface" />
      <div className="absolute top-1/4 left-1/3 w-96 h-96 bg-primary-blue/5 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/3 w-96 h-96 bg-primary-purple/5 rounded-full blur-3xl" />

      <motion.div
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        variants={containerVariants}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            Choose Your
            <span className="bg-gradient-to-r from-primary-blue via-primary-purple to-primary-cyan bg-clip-text text-transparent">
              {' '}Reporting Plan
            </span>
          </h2>
          
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Start free and upgrade as your reporting needs grow. All plans include 
            our core security features and cross-border support.
          </p>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-6">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              variants={cardVariants}
              whileHover="hover"
              className={`relative ${plan.popular ? 'lg:-mt-4 lg:mb-4' : ''}`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <motion.div
                  className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5, duration: 0.5 }}
                >
                  <div className="bg-gradient-to-r from-primary-blue to-primary-purple px-6 py-2 rounded-full text-white text-sm font-semibold shadow-glow-blue">
                    Most Popular
                  </div>
                </motion.div>
              )}

              <Card
                variant="glass"
                padding="none"
                className={`h-full ${plan.popular ? 'border-primary-blue/50 shadow-glow-blue' : 'border-white/10'} transition-all duration-300`}
                hover={false}
              >
                <div className="p-8">
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${plan.color} mb-4`}>
                      <plan.icon className="w-8 h-8 text-white" />
                    </div>
                    
                    <h3 className="text-2xl font-bold text-white mb-2">
                      {plan.name}
                    </h3>
                    
                    <p className="text-gray-400 text-sm mb-6">
                      {plan.description}
                    </p>
                    
                    <div className="mb-6">
                      <span className="text-4xl lg:text-5xl font-bold text-white">
                        ${plan.price}
                      </span>
                      <span className="text-gray-400 ml-2">
                        {plan.period}
                      </span>
                    </div>
                    
                    <Button
                      variant={plan.popular ? 'primary' : 'outline'}
                      size="lg"
                      className={`w-full ${plan.popular ? 'glow' : ''}`}
                      glow={plan.popular}
                    >
                      {plan.name === 'Free' ? 'Get Started' : 
                       plan.name === 'Enterprise' ? 'Contact Sales' : 'Start Free Trial'}
                    </Button>
                  </div>

                  {/* Features List */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-white mb-4">
                      What's included:
                    </h4>
                    
                    {plan.features.map((feature, featureIndex) => (
                      <motion.div
                        key={feature}
                        className="flex items-start space-x-3"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ delay: featureIndex * 0.1 + index * 0.2 }}
                      >
                        <Check className="w-5 h-5 text-accent-green mt-0.5 flex-shrink-0" />
                        <span className="text-gray-300 text-sm">
                          {feature}
                        </span>
                      </motion.div>
                    ))}
                    
                    {plan.limitations.length > 0 && (
                      <div className="pt-4 mt-6 border-t border-white/10">
                        <h5 className="text-sm font-medium text-gray-400 mb-3">
                          Limitations:
                        </h5>
                        {plan.limitations.map((limitation, limitIndex) => (
                          <div
                            key={limitation}
                            className="flex items-start space-x-3 mb-2"
                          >
                            <div className="w-5 h-5 mt-0.5 flex-shrink-0">
                              <div className="w-2 h-2 bg-gray-500 rounded-full mt-1.5 mx-auto" />
                            </div>
                            <span className="text-gray-500 text-sm">
                              {limitation}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Card Glow Effect */}
                {plan.popular && (
                  <div className="absolute inset-0 bg-gradient-to-br from-primary-blue/10 via-transparent to-primary-purple/10 rounded-2xl -z-10" />
                )}
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Card
            variant="glass"
            padding="xl"
            className="max-w-4xl mx-auto"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <Crown className="w-8 h-8 text-accent-orange mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-white mb-2">
                  30-Day Free Trial
                </h3>
                <p className="text-gray-400 text-sm">
                  Try Premium features risk-free for 30 days
                </p>
              </div>
              
              <div>
                <Check className="w-8 h-8 text-accent-green mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-white mb-2">
                  No Setup Fees
                </h3>
                <p className="text-gray-400 text-sm">
                  Get started immediately with no hidden costs
                </p>
              </div>
              
              <div>
                <Zap className="w-8 h-8 text-primary-blue mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-white mb-2">
                  Cancel Anytime
                </h3>
                <p className="text-gray-400 text-sm">
                  Flexible plans that grow with your needs
                </p>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default Pricing;
