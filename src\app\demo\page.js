import Header from '@/components/layout/Header';
import ParticleField from '@/components/effects/ParticleField';

export default function DemoPage() {
  return (
    <main className="relative min-h-screen">
      {/* Background Effects */}
      <ParticleField 
        particleCount={60}
        color="#6366F1"
        size={2}
        speed={0.2}
        interactive={true}
      />
      
      {/* Header */}
      <Header />
      
      {/* Demo Content */}
      <div className="relative z-10 pt-24 pb-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6">
              <span className="bg-gradient-to-r from-primary-purple via-primary-blue to-primary-cyan bg-clip-text text-transparent">
                Interactive Demo
              </span>
            </h1>
            
            <p className="text-xl text-gray-400 max-w-3xl mx-auto mb-12">
              Experience the full ReportU platform with our comprehensive demo featuring 
              real-world reporting scenarios and simulated backend responses.
            </p>
            
            <div className="glass p-8 rounded-2xl max-w-4xl mx-auto">
              <h2 className="text-2xl font-bold text-white mb-6">
                🚧 Demo Coming Soon
              </h2>
              <p className="text-gray-300 mb-8">
                We're building an incredible interactive demo with multiple layers:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 text-left">
                <div className="glass-dark p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-primary-blue mb-3">
                    📝 Report Creation
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Interactive form with real-time validation, category selection, and location picker.
                  </p>
                </div>
                
                <div className="glass-dark p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-primary-purple mb-3">
                    📸 Evidence Upload
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Simulated photo/video upload with compression and metadata extraction.
                  </p>
                </div>
                
                <div className="glass-dark p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-primary-cyan mb-3">
                    🤖 AI Processing
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Watch AI categorize and route reports to the correct authorities.
                  </p>
                </div>
                
                <div className="glass-dark p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-accent-green mb-3">
                    📊 Status Tracking
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Real-time status updates and authority response simulation.
                  </p>
                </div>
                
                <div className="glass-dark p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-accent-orange mb-3">
                    📈 Analytics
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Community safety metrics and personal report history dashboard.
                  </p>
                </div>
                
                <div className="glass-dark p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-accent-pink mb-3">
                    🔔 Notifications
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Live notification system with SMS and email simulation.
                  </p>
                </div>
              </div>
              
              <div className="mt-8 p-6 bg-gradient-to-r from-primary-blue/20 to-primary-purple/20 rounded-xl border border-primary-blue/30">
                <h3 className="text-lg font-semibold text-white mb-2">
                  🎯 Development Progress
                </h3>
                <p className="text-gray-300 text-sm mb-4">
                  The interactive demo is currently being built with real-world functionality, 
                  not just static designs. Each layer will demonstrate actual working features.
                </p>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div className="bg-gradient-to-r from-primary-blue to-primary-purple h-2 rounded-full" style={{width: '35%'}}></div>
                </div>
                <p className="text-xs text-gray-400 mt-2">35% Complete - Coming Soon!</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
