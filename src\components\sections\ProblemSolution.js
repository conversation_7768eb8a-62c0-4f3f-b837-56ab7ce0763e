'use client';

import { motion } from 'framer-motion';
import { 
  <PERSON>ert<PERSON><PERSON>gle, 
  Clock, 
  Users, 
  ArrowRight, 
  CheckCircle, 
  Zap, 
  Shield, 
  Globe 
} from 'lucide-react';
import Card from '../ui/Card';

const ProblemSolution = () => {
  const problems = [
    {
      icon: AlertTriangle,
      title: 'Complex Reporting Processes',
      description: 'Citizens struggle with navigating multiple platforms and departments, leading to confusion and delays in reporting critical incidents.',
      stats: '73% of citizens find current systems too complex'
    },
    {
      icon: Clock,
      title: 'Lack of Immediate Action',
      description: 'Time-consuming procedures discourage witnesses from reporting, with uncertainty about which authority to approach.',
      stats: 'Average report time: 45+ minutes'
    },
    {
      icon: Users,
      title: 'Fragmented Systems',
      description: 'No unified platform exists for cross-border incidents between Malaysia and Singapore, creating jurisdiction confusion.',
      stats: '2 separate systems, 0 integration'
    }
  ];

  const solutions = [
    {
      icon: Zap,
      title: 'Lightning-Fast Reporting',
      description: 'Submit comprehensive reports in under 2 minutes with our AI-powered categorization and smart forms.',
      benefit: '95% faster than traditional methods'
    },
    {
      icon: Shield,
      title: 'Intelligent Routing',
      description: 'Automatic routing to the correct authorities based on incident type, location, and jurisdiction.',
      benefit: '100% accurate authority matching'
    },
    {
      icon: Globe,
      title: 'Unified Cross-Border Platform',
      description: 'Single platform supporting both Malaysian and Singaporean jurisdictions with real-time coordination.',
      benefit: 'First-ever unified system'
    }
  ];

  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 30 },
    animate: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const slideVariants = {
    initial: { opacity: 0, x: -50 },
    animate: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };

  return (
    <section className="relative py-20 lg:py-32 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-bg via-dark-surface to-dark-bg" />
      <div className="absolute top-1/4 left-0 w-96 h-96 bg-red-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-0 w-96 h-96 bg-green-500/10 rounded-full blur-3xl" />

      <motion.div
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        variants={containerVariants}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Section Header */}
        <motion.div
          className="text-center mb-20"
          variants={itemVariants}
        >
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            From
            <span className="text-red-400"> Frustration </span>
            to
            <span className="bg-gradient-to-r from-primary-blue to-accent-green bg-clip-text text-transparent"> Solution</span>
          </motion.h2>
          
          <motion.p
            className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            We identified the critical pain points in current reporting systems and built 
            a revolutionary solution that transforms how citizens report offenses.
          </motion.p>
        </motion.div>

        {/* Problem vs Solution Layout */}
        <div className="grid lg:grid-cols-2 gap-16 lg:gap-20 items-start">
          
          {/* Problems Section */}
          <motion.div
            variants={slideVariants}
            className="space-y-8"
          >
            <div className="text-center lg:text-left mb-12">
              <h3 className="text-2xl lg:text-3xl font-bold text-red-400 mb-4 flex items-center justify-center lg:justify-start">
                <AlertTriangle className="w-8 h-8 mr-3" />
                Current Problems
              </h3>
              <p className="text-gray-400">
                The challenges citizens face with existing reporting systems
              </p>
            </div>

            {problems.map((problem, index) => (
              <motion.div
                key={problem.title}
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card
                  variant="glass"
                  padding="lg"
                  className="border-red-500/20 hover:border-red-400/40 transition-all duration-300"
                >
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-red-500/20 rounded-xl">
                      <problem.icon className="w-6 h-6 text-red-400" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-white mb-2">
                        {problem.title}
                      </h4>
                      <p className="text-gray-400 text-sm mb-3 leading-relaxed">
                        {problem.description}
                      </p>
                      <div className="inline-flex items-center px-3 py-1 bg-red-500/10 rounded-full">
                        <span className="text-red-400 text-xs font-medium">
                          {problem.stats}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Arrow Transition */}
          <motion.div
            className="hidden lg:flex absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20"
            initial={{ opacity: 0, scale: 0 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <div className="p-4 bg-gradient-to-r from-red-500 to-green-500 rounded-full shadow-glow">
              <ArrowRight className="w-8 h-8 text-white" />
            </div>
          </motion.div>

          {/* Solutions Section */}
          <motion.div
            variants={slideVariants}
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="space-y-8"
          >
            <div className="text-center lg:text-left mb-12">
              <h3 className="text-2xl lg:text-3xl font-bold text-accent-green mb-4 flex items-center justify-center lg:justify-start">
                <CheckCircle className="w-8 h-8 mr-3" />
                Our Solutions
              </h3>
              <p className="text-gray-400">
                How ReportU revolutionizes the reporting experience
              </p>
            </div>

            {solutions.map((solution, index) => (
              <motion.div
                key={solution.title}
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 + 0.4 }}
              >
                <Card
                  variant="glass"
                  padding="lg"
                  className="border-green-500/20 hover:border-green-400/40 transition-all duration-300"
                >
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-green-500/20 rounded-xl">
                      <solution.icon className="w-6 h-6 text-accent-green" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-white mb-2">
                        {solution.title}
                      </h4>
                      <p className="text-gray-400 text-sm mb-3 leading-relaxed">
                        {solution.description}
                      </p>
                      <div className="inline-flex items-center px-3 py-1 bg-green-500/10 rounded-full">
                        <span className="text-accent-green text-xs font-medium">
                          {solution.benefit}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Impact Statistics */}
        <motion.div
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Card
            variant="gradient"
            padding="xl"
            className="max-w-4xl mx-auto"
          >
            <h3 className="text-2xl font-bold text-white mb-8">
              The ReportU Impact
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <motion.div
                  className="text-4xl font-bold text-accent-green mb-2"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 1 }}
                >
                  95%
                </motion.div>
                <p className="text-gray-300">Faster Reporting</p>
              </div>
              
              <div className="text-center">
                <motion.div
                  className="text-4xl font-bold text-primary-blue mb-2"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                >
                  100%
                </motion.div>
                <p className="text-gray-300">Accurate Routing</p>
              </div>
              
              <div className="text-center">
                <motion.div
                  className="text-4xl font-bold text-primary-purple mb-2"
                  initial={{ scale: 0 }}
                  whileInView={{ scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 1.2 }}
                >
                  24/7
                </motion.div>
                <p className="text-gray-300">Availability</p>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default ProblemSolution;
