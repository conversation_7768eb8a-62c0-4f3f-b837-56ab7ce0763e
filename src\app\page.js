import Header from '@/components/layout/Header';
import Hero from '@/components/sections/Hero';
import ProblemSolution from '@/components/sections/ProblemSolution';
import Features from '@/components/sections/Features';
import ParticleField from '@/components/effects/ParticleField';

export default function Home() {
  return (
    <main className="relative">
      {/* Background Effects */}
      <ParticleField
        particleCount={80}
        color="#0066FF"
        size={3}
        speed={0.3}
        interactive={true}
      />

      {/* Header */}
      <Header />

      {/* Hero Section */}
      <Hero />

      {/* Problem/Solution Section */}
      <ProblemSolution />

      {/* Features Section */}
      <Features />

      {/* Additional sections will be added here */}
    </main>
  );
}
