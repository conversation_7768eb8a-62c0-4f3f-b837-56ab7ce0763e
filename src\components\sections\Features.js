'use client';

import { motion } from 'framer-motion';
import { 
  Zap, 
  Shield, 
  Globe, 
  Camera, 
  MapPin, 
  Bell, 
  BarChart3, 
  Clock,
  Users,
  CheckCircle
} from 'lucide-react';
import Card from '../ui/Card';

const Features = () => {
  const features = [
    {
      icon: Zap,
      title: 'Lightning Fast Reports',
      description: 'Submit offense reports in under 2 minutes with our streamlined interface.',
      color: 'from-yellow-400 to-orange-500',
      delay: 0.1
    },
    {
      icon: Shield,
      title: 'Secure & Encrypted',
      description: 'Military-grade encryption protects your data and evidence files.',
      color: 'from-blue-400 to-blue-600',
      delay: 0.2
    },
    {
      icon: Globe,
      title: 'Cross-Border Support',
      description: 'Seamlessly report incidents across Malaysia and Singapore jurisdictions.',
      color: 'from-green-400 to-emerald-600',
      delay: 0.3
    },
    {
      icon: Camera,
      title: 'Multimedia Evidence',
      description: 'Upload photos, videos, and audio evidence with automatic compression.',
      color: 'from-purple-400 to-purple-600',
      delay: 0.4
    },
    {
      icon: MapPin,
      title: 'GPS Location Tracking',
      description: 'Automatic location detection and verification for accurate reporting.',
      color: 'from-red-400 to-pink-600',
      delay: 0.5
    },
    {
      icon: Bell,
      title: 'Real-time Updates',
      description: 'Get instant notifications about your report status and authority responses.',
      color: 'from-cyan-400 to-blue-500',
      delay: 0.6
    },
    {
      icon: BarChart3,
      title: 'Analytics Dashboard',
      description: 'Track community safety trends and your personal report history.',
      color: 'from-indigo-400 to-purple-500',
      delay: 0.7
    },
    {
      icon: Clock,
      title: '24/7 Availability',
      description: 'Report incidents anytime, anywhere with our always-on platform.',
      color: 'from-teal-400 to-green-500',
      delay: 0.8
    }
  ];

  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 30 },
    animate: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  const cardVariants = {
    initial: { opacity: 0, scale: 0.8, y: 50 },
    animate: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" }
    },
    hover: {
      y: -10,
      scale: 1.02,
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };

  return (
    <section className="relative py-20 lg:py-32 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-dark-bg via-dark-surface/50 to-dark-bg" />
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary-blue/10 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-primary-purple/10 rounded-full blur-3xl" />

      <motion.div
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
        variants={containerVariants}
        initial="initial"
        whileInView="animate"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          variants={itemVariants}
        >
          <motion.h2
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            Powerful Features for
            <span className="bg-gradient-to-r from-primary-blue via-primary-purple to-primary-cyan bg-clip-text text-transparent">
              {' '}Modern Reporting
            </span>
          </motion.h2>
          
          <motion.p
            className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Experience the future of offense reporting with cutting-edge technology designed 
            for speed, security, and seamless cross-border functionality.
          </motion.p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8"
          variants={containerVariants}
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              variants={cardVariants}
              whileHover="hover"
              transition={{ delay: feature.delay }}
            >
              <Card
                variant="glass"
                padding="lg"
                className="h-full group cursor-pointer"
                hover={false} // We're handling hover manually
              >
                <div className="flex flex-col items-center text-center space-y-4">
                  {/* Icon */}
                  <motion.div
                    className={`p-4 rounded-2xl bg-gradient-to-br ${feature.color} shadow-lg group-hover:shadow-xl transition-all duration-300`}
                    whileHover={{ 
                      scale: 1.1,
                      rotate: 5,
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <feature.icon className="w-8 h-8 text-white" />
                  </motion.div>

                  {/* Content */}
                  <div className="space-y-3">
                    <motion.h3
                      className="text-xl font-semibold text-white group-hover:text-primary-blue transition-colors duration-300"
                      whileHover={{ scale: 1.05 }}
                    >
                      {feature.title}
                    </motion.h3>
                    
                    <p className="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors duration-300">
                      {feature.description}
                    </p>
                  </div>

                  {/* Hover Effect Indicator */}
                  <motion.div
                    className="w-0 h-0.5 bg-gradient-to-r from-primary-blue to-primary-purple group-hover:w-full transition-all duration-300"
                    initial={{ width: 0 }}
                    whileHover={{ width: '100%' }}
                  />
                </div>

                {/* Background Glow Effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-primary-blue/5 via-transparent to-primary-purple/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                />
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA Section */}
        <motion.div
          className="mt-20 text-center"
          variants={itemVariants}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
        >
          <Card
            variant="glass"
            padding="xl"
            className="max-w-4xl mx-auto"
          >
            <div className="flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0 lg:space-x-8">
              <div className="flex-1 text-left">
                <div className="flex items-center space-x-3 mb-4">
                  <CheckCircle className="w-6 h-6 text-accent-green" />
                  <h3 className="text-2xl font-bold text-white">Ready to Get Started?</h3>
                </div>
                <p className="text-gray-400 leading-relaxed">
                  Join thousands of citizens making their communities safer with ReportU's 
                  advanced reporting platform.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                <motion.button
                  className="px-8 py-3 bg-gradient-to-r from-primary-blue to-primary-purple text-white font-semibold rounded-xl hover:shadow-glow-blue transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Start Reporting
                </motion.button>
                <motion.button
                  className="px-8 py-3 border border-white/30 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Learn More
                </motion.button>
              </div>
            </div>
          </Card>
        </motion.div>
      </motion.div>

      {/* Floating Animation Elements */}
      <motion.div
        className="absolute top-1/4 left-10 w-4 h-4 bg-primary-cyan rounded-full opacity-60"
        animate={{
          y: [0, -30, 0],
          x: [0, 20, 0],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      
      <motion.div
        className="absolute bottom-1/4 right-20 w-6 h-6 bg-accent-pink rounded-full opacity-40"
        animate={{
          y: [0, 40, 0],
          x: [0, -30, 0],
          scale: [1, 0.8, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
      />
    </section>
  );
};

export default Features;
