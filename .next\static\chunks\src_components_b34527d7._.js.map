{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Button = forwardRef(({ \n  children, \n  variant = 'primary', \n  size = 'md', \n  className = '', \n  disabled = false,\n  loading = false,\n  glow = false,\n  onClick,\n  type = 'button',\n  ...props \n}, ref) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-bg disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-primary-blue hover:bg-blue-600 text-white focus:ring-primary-blue shadow-lg hover:shadow-glow-blue',\n    secondary: 'bg-primary-purple hover:bg-purple-600 text-white focus:ring-primary-purple shadow-lg hover:shadow-glow-purple',\n    accent: 'bg-accent-pink hover:bg-pink-600 text-white focus:ring-accent-pink shadow-lg hover:shadow-glow',\n    outline: 'border-2 border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white focus:ring-primary-blue',\n    ghost: 'text-white hover:bg-white/10 focus:ring-white/20',\n    glass: 'glass text-white hover:bg-white/20 focus:ring-white/20 backdrop-blur-xl',\n  };\n  \n  const sizes = {\n    sm: 'px-4 py-2 text-sm',\n    md: 'px-6 py-3 text-base',\n    lg: 'px-8 py-4 text-lg',\n    xl: 'px-10 py-5 text-xl',\n  };\n  \n  const glowClasses = glow ? 'btn-glow' : '';\n  \n  const buttonClasses = `${baseClasses} ${variants[variant]} ${sizes[size]} ${glowClasses} ${className}`;\n  \n  const buttonVariants = {\n    initial: { scale: 1 },\n    hover: { \n      scale: 1.05,\n      transition: { duration: 0.2 }\n    },\n    tap: { \n      scale: 0.95,\n      transition: { duration: 0.1 }\n    }\n  };\n  \n  return (\n    <motion.button\n      ref={ref}\n      type={type}\n      className={buttonClasses}\n      disabled={disabled || loading}\n      onClick={onClick}\n      variants={buttonVariants}\n      initial=\"initial\"\n      whileHover=\"hover\"\n      whileTap=\"tap\"\n      {...props}\n    >\n      {loading && (\n        <motion.div\n          className=\"mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full\"\n          animate={{ rotate: 360 }}\n          transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n        />\n      )}\n      {children}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACzB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,KAAK,EACZ,OAAO,EACP,OAAO,QAAQ,EACf,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,QAAQ;QACR,SAAS;QACT,OAAO;QACP,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,OAAO,aAAa;IAExC,MAAM,gBAAgB,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,WAAW;IAEtG,MAAM,iBAAiB;QACrB,SAAS;YAAE,OAAO;QAAE;QACpB,OAAO;YACL,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,KAAK;YACH,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,MAAM;QACN,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACT,UAAU;QACV,SAAQ;QACR,YAAW;QACX,UAAS;QACR,GAAG,KAAK;;YAER,yBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAI;gBACvB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAS;;;;;;YAG/D;;;;;;;AAGP;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\nimport { Menu, X, Shield, Zap } from 'lucide-react';\nimport Button from '../ui/Button';\n\nconst Header = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Demo', href: '/demo' },\n    { name: 'About', href: '/about' },\n    { name: 'Contact', href: '/contact' },\n  ];\n\n  const headerVariants = {\n    initial: { y: -100, opacity: 0 },\n    animate: { \n      y: 0, \n      opacity: 1,\n      transition: { duration: 0.6, ease: \"easeOut\" }\n    }\n  };\n\n  const logoVariants = {\n    initial: { scale: 0, rotate: -180 },\n    animate: { \n      scale: 1, \n      rotate: 0,\n      transition: { duration: 0.8, ease: \"easeOut\", delay: 0.2 }\n    },\n    hover: {\n      scale: 1.1,\n      rotate: 5,\n      transition: { duration: 0.3 }\n    }\n  };\n\n  const navItemVariants = {\n    initial: { opacity: 0, y: -20 },\n    animate: { \n      opacity: 1, \n      y: 0,\n      transition: { duration: 0.5, ease: \"easeOut\" }\n    },\n    hover: {\n      scale: 1.05,\n      color: '#0066FF',\n      transition: { duration: 0.2 }\n    }\n  };\n\n  const mobileMenuVariants = {\n    initial: { opacity: 0, height: 0 },\n    animate: { \n      opacity: 1, \n      height: 'auto',\n      transition: { duration: 0.3, ease: \"easeOut\" }\n    },\n    exit: { \n      opacity: 0, \n      height: 0,\n      transition: { duration: 0.3, ease: \"easeIn\" }\n    }\n  };\n\n  return (\n    <motion.header\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled \n          ? 'glass backdrop-blur-xl border-b border-white/10' \n          : 'bg-transparent'\n      }`}\n      variants={headerVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex items-center space-x-3\"\n            variants={logoVariants}\n            whileHover=\"hover\"\n          >\n            <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n              {/* Custom SVG Logo */}\n              <motion.div\n                className=\"relative\"\n                whileHover={{ rotate: 360 }}\n                transition={{ duration: 0.8 }}\n              >\n                <div className=\"w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-primary-blue to-primary-purple rounded-xl flex items-center justify-center shadow-glow-blue\">\n                  <Shield className=\"w-6 h-6 lg:w-7 lg:h-7 text-white\" />\n                </div>\n                <motion.div\n                  className=\"absolute -top-1 -right-1 w-4 h-4 bg-accent-green rounded-full flex items-center justify-center\"\n                  animate={{ scale: [1, 1.2, 1] }}\n                  transition={{ duration: 2, repeat: Infinity }}\n                >\n                  <Zap className=\"w-2 h-2 text-white\" />\n                </motion.div>\n              </motion.div>\n              \n              {/* Brand Name */}\n              <div className=\"flex flex-col\">\n                <motion.span\n                  className=\"text-xl lg:text-2xl font-bold text-white group-hover:text-primary-blue transition-colors duration-300\"\n                  whileHover={{ scale: 1.05 }}\n                >\n                  ReportU\n                </motion.span>\n                <motion.span\n                  className=\"text-xs text-gray-400 group-hover:text-primary-cyan transition-colors duration-300\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.5 }}\n                >\n                  Cross-Border Reporting\n                </motion.span>\n              </div>\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navigation.map((item, index) => (\n              <motion.div\n                key={item.name}\n                variants={navItemVariants}\n                initial=\"initial\"\n                animate=\"animate\"\n                whileHover=\"hover\"\n                transition={{ delay: index * 0.1 }}\n              >\n                <Link\n                  href={item.href}\n                  className=\"text-gray-300 hover:text-white font-medium transition-colors duration-300 relative group\"\n                >\n                  {item.name}\n                  <motion.div\n                    className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-blue group-hover:w-full transition-all duration-300\"\n                    whileHover={{ width: '100%' }}\n                  />\n                </Link>\n              </motion.div>\n            ))}\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white\"\n            >\n              Try Demo\n            </Button>\n            <Button\n              variant=\"primary\"\n              size=\"sm\"\n              glow\n            >\n              Report Now\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            className=\"lg:hidden p-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-colors duration-300\"\n            onClick={() => setIsOpen(!isOpen)}\n            whileTap={{ scale: 0.95 }}\n          >\n            <AnimatePresence mode=\"wait\">\n              {isOpen ? (\n                <motion.div\n                  key=\"close\"\n                  initial={{ rotate: -90, opacity: 0 }}\n                  animate={{ rotate: 0, opacity: 1 }}\n                  exit={{ rotate: 90, opacity: 0 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <X className=\"w-6 h-6\" />\n                </motion.div>\n              ) : (\n                <motion.div\n                  key=\"menu\"\n                  initial={{ rotate: 90, opacity: 0 }}\n                  animate={{ rotate: 0, opacity: 1 }}\n                  exit={{ rotate: -90, opacity: 0 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <Menu className=\"w-6 h-6\" />\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </motion.button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"lg:hidden\"\n              variants={mobileMenuVariants}\n              initial=\"initial\"\n              animate=\"animate\"\n              exit=\"exit\"\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 glass rounded-xl mt-2 border border-white/10\">\n                {navigation.map((item, index) => (\n                  <motion.div\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      className=\"block px-3 py-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors duration-300\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      {item.name}\n                    </Link>\n                  </motion.div>\n                ))}\n                <div className=\"pt-4 space-y-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"w-full border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white\"\n                  >\n                    Try Demo\n                  </Button>\n                  <Button\n                    variant=\"primary\"\n                    size=\"sm\"\n                    className=\"w-full\"\n                    glow\n                  >\n                    Report Now\n                  </Button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,SAAS;;IACb,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,iBAAiB;QACrB,SAAS;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC/B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QAClC,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBAAE,UAAU;gBAAK,MAAM;gBAAW,OAAO;YAAI;QAC3D;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,kBAAkB;QACtB,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;QACA,OAAO;YACL,OAAO;YACP,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,qBAAqB;QACzB,SAAS;YAAE,SAAS;YAAG,QAAQ;QAAE;QACjC,SAAS;YACP,SAAS;YACT,QAAQ;YACR,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;QACA,MAAM;YACJ,SAAS;YACT,QAAQ;YACR,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAC,4DAA4D,EACtE,WACI,oDACA,kBACJ;QACF,UAAU;QACV,SAAQ;QACR,SAAQ;kBAER,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;4BACV,YAAW;sCAEX,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDAEvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,QAAQ;wCAAI;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;0DAE5C,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAK;0DAC3B;;;;;;0DAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,WAAU;gDACV,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS;oDAAE,SAAS;gDAAE;gDACtB,YAAY;oDAAE,OAAO;gDAAI;0DAC1B;;;;;;;;;;;;;;;;;;;;;;;sCAQP,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,YAAW;oCACX,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,IAAI;0DACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAO;;;;;;;;;;;;mCAd3B,KAAK,IAAI;;;;;;;;;;sCAsBpB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,IAAI;8CACL;;;;;;;;;;;;sCAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,SAAS,IAAM,UAAU,CAAC;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACnB,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,QAAQ,CAAC;wCAAI,SAAS;oCAAE;oCACnC,SAAS;wCAAE,QAAQ;wCAAG,SAAS;oCAAE;oCACjC,MAAM;wCAAE,QAAQ;wCAAI,SAAS;oCAAE;oCAC/B,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;mCANT;;;;yDASN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,QAAQ;wCAAI,SAAS;oCAAE;oCAClC,SAAS;wCAAE,QAAQ;wCAAG,SAAS;oCAAE;oCACjC,MAAM;wCAAE,QAAQ,CAAC;wCAAI,SAAS;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;mCANZ;;;;;;;;;;;;;;;;;;;;;8BAcd,6LAAC,4LAAA,CAAA,kBAAe;8BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,MAAK;kCAEL,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,UAAU;sDAExB,KAAK,IAAI;;;;;;uCAVP,KAAK,IAAI;;;;;8CAclB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,oIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,IAAI;sDACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA/PM;KAAA;uCAiQS", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/ui/Card.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Card = forwardRef(({ \n  children, \n  variant = 'glass', \n  className = '', \n  hover = true,\n  glow = false,\n  padding = 'md',\n  ...props \n}, ref) => {\n  const baseClasses = 'relative overflow-hidden transition-all duration-300';\n  \n  const variants = {\n    glass: 'glass',\n    'glass-dark': 'glass-dark',\n    solid: 'bg-dark-surface border border-white/10',\n    gradient: 'bg-gradient-to-br from-primary-blue/20 to-primary-purple/20 border border-white/10',\n    aurora: 'aurora-bg opacity-90',\n  };\n  \n  const paddings = {\n    none: '',\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8',\n    xl: 'p-10',\n  };\n  \n  const glowClasses = glow ? 'shadow-glow hover:shadow-glow-lg' : '';\n  const hoverClasses = hover ? 'hover:scale-105 hover:-translate-y-1' : '';\n  \n  const cardClasses = `${baseClasses} ${variants[variant]} ${paddings[padding]} ${glowClasses} ${hoverClasses} ${className}`;\n  \n  const cardVariants = {\n    initial: { \n      opacity: 0,\n      y: 20,\n      scale: 0.95\n    },\n    animate: { \n      opacity: 1,\n      y: 0,\n      scale: 1,\n      transition: {\n        duration: 0.5,\n        ease: \"easeOut\"\n      }\n    },\n    hover: hover ? {\n      y: -8,\n      scale: 1.02,\n      transition: {\n        duration: 0.3,\n        ease: \"easeOut\"\n      }\n    } : {},\n  };\n  \n  return (\n    <motion.div\n      ref={ref}\n      className={cardClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      {...props}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-white/5\" />\n        <div className=\"absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent\" />\n        <div className=\"absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent\" />\n      </div>\n      \n      {/* Content */}\n      <div className=\"relative z-10\">\n        {children}\n      </div>\n      \n      {/* Glow Effect */}\n      {glow && (\n        <div className=\"absolute inset-0 -z-10 bg-gradient-to-r from-primary-blue/20 via-primary-purple/20 to-primary-cyan/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n      )}\n    </motion.div>\n  );\n});\n\nCard.displayName = 'Card';\n\nexport default Card;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACvB,QAAQ,EACR,UAAU,OAAO,EACjB,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,OAAO,KAAK,EACZ,UAAU,IAAI,EACd,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,OAAO;QACP,cAAc;QACd,OAAO;QACP,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,WAAW;QACf,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,OAAO,qCAAqC;IAChE,MAAM,eAAe,QAAQ,yCAAyC;IAEtE,MAAM,cAAc,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,aAAa,CAAC,EAAE,WAAW;IAE1H,MAAM,eAAe;QACnB,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;QACT;QACA,SAAS;YACP,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF,IAAI,CAAC;IACP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACV,GAAG,KAAK;;0BAGT,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIF,sBACC,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAIvB;;AAEA,KAAK,WAAW,GAAG;uCAEJ", "debugId": null}}, {"offset": {"line": 745, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/sections/Hero.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { ArrowRight, Shield, Zap, Globe, Users } from 'lucide-react';\nimport Button from '../ui/Button';\nimport Card from '../ui/Card';\n\nconst Hero = () => {\n  const [typedText, setTypedText] = useState('');\n  const fullText = 'Cross-Border Offense Reporting Platform';\n\n  useEffect(() => {\n    let index = 0;\n    const timer = setInterval(() => {\n      if (index <= fullText.length) {\n        setTypedText(fullText.slice(0, index));\n        index++;\n      } else {\n        clearInterval(timer);\n      }\n    }, 100);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  const containerVariants = {\n    initial: { opacity: 0 },\n    animate: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    initial: { opacity: 0, y: 30 },\n    animate: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.6, ease: \"easeOut\" }\n    }\n  };\n\n  const statsVariants = {\n    initial: { scale: 0, opacity: 0 },\n    animate: {\n      scale: 1,\n      opacity: 1,\n      transition: { duration: 0.5, ease: \"easeOut\" }\n    }\n  };\n\n  const stats = [\n    { icon: Users, value: '39.9M', label: 'Potential Users' },\n    { icon: Globe, value: '2', label: 'Countries' },\n    { icon: Shield, value: '24/7', label: 'Protection' },\n    { icon: Zap, value: '<2min', label: 'Report Time' },\n  ];\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 aurora-bg opacity-20\" />\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-dark-bg/50 to-dark-bg\" />\n      \n      <motion.div\n        className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\"\n        variants={containerVariants}\n        initial=\"initial\"\n        animate=\"animate\"\n      >\n        <div className=\"text-center\">\n          {/* Main Heading */}\n          <motion.div\n            className=\"mb-8\"\n            variants={itemVariants}\n          >\n            <motion.h1\n              className=\"text-4xl sm:text-5xl lg:text-7xl font-bold text-white mb-4\"\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              transition={{ duration: 0.8, ease: \"easeOut\" }}\n            >\n              <span className=\"bg-gradient-to-r from-primary-blue via-primary-purple to-primary-cyan bg-clip-text text-transparent\">\n                ReportU\n              </span>\n            </motion.h1>\n            \n            <motion.div\n              className=\"text-xl sm:text-2xl lg:text-3xl text-gray-300 font-medium h-12 flex items-center justify-center\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.5, duration: 0.8 }}\n            >\n              {typedText}\n              <motion.span\n                className=\"ml-1 w-0.5 h-8 bg-primary-blue\"\n                animate={{ opacity: [1, 0, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              />\n            </motion.div>\n          </motion.div>\n\n          {/* Subtitle */}\n          <motion.p\n            className=\"text-lg sm:text-xl text-gray-400 max-w-3xl mx-auto mb-12 leading-relaxed\"\n            variants={itemVariants}\n          >\n            Revolutionary platform for <span className=\"text-primary-cyan font-semibold\">Malaysia</span> and{' '}\n            <span className=\"text-accent-pink font-semibold\">Singapore</span> citizens to seamlessly submit offense reports.\n            Quick, secure, and automatically routed to the right authorities.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\"\n            variants={itemVariants}\n          >\n            <Button\n              variant=\"primary\"\n              size=\"lg\"\n              glow\n              className=\"group\"\n            >\n              Start Reporting Now\n              <ArrowRight className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n            </Button>\n            \n            <Button\n              variant=\"outline\"\n              size=\"lg\"\n              className=\"border-white/30 text-white hover:bg-white/10\"\n            >\n              Watch Demo\n            </Button>\n          </motion.div>\n\n          {/* Stats Grid */}\n          <motion.div\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto\"\n            variants={itemVariants}\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                variants={statsVariants}\n                transition={{ delay: index * 0.1 }}\n              >\n                <Card\n                  variant=\"glass\"\n                  padding=\"md\"\n                  hover={true}\n                  className=\"text-center group\"\n                >\n                  <motion.div\n                    className=\"flex flex-col items-center space-y-3\"\n                    whileHover={{ scale: 1.05 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <div className=\"p-3 rounded-xl bg-gradient-to-br from-primary-blue to-primary-purple group-hover:shadow-glow-blue transition-all duration-300\">\n                      <stat.icon className=\"w-6 h-6 text-white\" />\n                    </div>\n                    \n                    <div>\n                      <motion.div\n                        className=\"text-2xl lg:text-3xl font-bold text-white mb-1\"\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        transition={{ delay: 0.8 + index * 0.1, duration: 0.5, type: \"spring\" }}\n                      >\n                        {stat.value}\n                      </motion.div>\n                      <div className=\"text-sm text-gray-400 font-medium\">\n                        {stat.label}\n                      </div>\n                    </div>\n                  </motion.div>\n                </Card>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div\n            className=\"mt-16 flex flex-col items-center\"\n            variants={itemVariants}\n          >\n            <p className=\"text-sm text-gray-500 mb-4\">Trusted by citizens across Malaysia and Singapore</p>\n            <div className=\"flex items-center space-x-8 opacity-60\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded flex items-center justify-center\">\n                  <span className=\"text-white text-xs font-bold\">MY</span>\n                </div>\n                <span className=\"text-gray-400 text-sm\">Malaysia</span>\n              </div>\n              <div className=\"w-px h-6 bg-gray-600\" />\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-br from-red-500 to-white rounded flex items-center justify-center\">\n                  <span className=\"text-red-600 text-xs font-bold\">SG</span>\n                </div>\n                <span className=\"text-gray-400 text-sm\">Singapore</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Floating Elements */}\n      <motion.div\n        className=\"absolute top-20 left-10 w-20 h-20 bg-primary-blue/20 rounded-full blur-xl\"\n        animate={{\n          y: [0, -20, 0],\n          scale: [1, 1.1, 1],\n        }}\n        transition={{\n          duration: 4,\n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }}\n      />\n      \n      <motion.div\n        className=\"absolute bottom-20 right-10 w-32 h-32 bg-primary-purple/20 rounded-full blur-xl\"\n        animate={{\n          y: [0, 20, 0],\n          scale: [1, 0.9, 1],\n        }}\n        transition={{\n          duration: 5,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 1\n        }}\n      />\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,OAAO;;IACX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,QAAQ;YACZ,MAAM,QAAQ;wCAAY;oBACxB,IAAI,SAAS,SAAS,MAAM,EAAE;wBAC5B,aAAa,SAAS,KAAK,CAAC,GAAG;wBAC/B;oBACF,OAAO;wBACL,cAAc;oBAChB;gBACF;uCAAG;YAEH;kCAAO,IAAM,cAAc;;QAC7B;yBAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;IACF;IAEA,MAAM,gBAAgB;QACpB,SAAS;YAAE,OAAO;YAAG,SAAS;QAAE;QAChC,SAAS;YACP,OAAO;YACP,SAAS;YACT,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;IACF;IAEA,MAAM,QAAQ;QACZ;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;YAAS,OAAO;QAAkB;QACxD;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;YAAK,OAAO;QAAY;QAC9C;YAAE,MAAM,yMAAA,CAAA,SAAM;YAAE,OAAO;YAAQ,OAAO;QAAa;QACnD;YAAE,MAAM,mMAAA,CAAA,MAAG;YAAE,OAAO;YAAS,OAAO;QAAc;KACnD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;0BAER,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,OAAO;wCAAK,SAAS;oCAAE;oCAClC,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,MAAM;oCAAU;8CAE7C,cAAA,6LAAC;wCAAK,WAAU;kDAAsG;;;;;;;;;;;8CAKxH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;wCAAK,UAAU;oCAAI;;wCAEvC;sDACD,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAU;4CACV,SAAS;gDAAE,SAAS;oDAAC;oDAAG;oDAAG;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;;;;;;;;;;;;;;;;;;sCAMlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,UAAU;;gCACX;8CAC4B,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;gCAAe;gCAAK;8CACjG,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;gCAAgB;;;;;;;sCAKnE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,IAAI;oCACJ,WAAU;;wCACX;sDAEC,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,6LAAC,oIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;sCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEjC,cAAA,6LAAC,kIAAA,CAAA,UAAI;wCACH,SAAQ;wCACR,SAAQ;wCACR,OAAO;wCACP,WAAU;kDAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;;8DAE5B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAGvB,6LAAC;;sEACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,OAAO;4DAAE;4DACpB,SAAS;gEAAE,OAAO;4DAAE;4DACpB,YAAY;gEAAE,OAAO,MAAM,QAAQ;gEAAK,UAAU;gEAAK,MAAM;4DAAS;sEAErE,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAI,WAAU;sEACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;mCA7Bd,KAAK,KAAK;;;;;;;;;;sCAuCrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAiC;;;;;;;;;;;8DAEnD,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;oBACb,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;;;;;;;;;;;;AAIR;GAvOM;KAAA;uCAyOS", "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i1-d2-ReportU/src/components/effects/ParticleField.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\n\nconst ParticleField = ({ \n  particleCount = 50, \n  color = '#0066FF', \n  size = 2, \n  speed = 0.5,\n  interactive = true,\n  className = ''\n}) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const particlesRef = useRef([]);\n  const mouseRef = useRef({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let particles = [];\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Particle class\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.vx = (Math.random() - 0.5) * speed;\n        this.vy = (Math.random() - 0.5) * speed;\n        this.size = Math.random() * size + 1;\n        this.opacity = Math.random() * 0.5 + 0.2;\n        this.life = Math.random() * 100 + 100;\n        this.maxLife = this.life;\n      }\n\n      update() {\n        this.x += this.vx;\n        this.y += this.vy;\n        this.life--;\n\n        // Mouse interaction\n        if (interactive) {\n          const dx = mouseRef.current.x - this.x;\n          const dy = mouseRef.current.y - this.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance < 100) {\n            const force = (100 - distance) / 100;\n            this.vx += (dx / distance) * force * 0.01;\n            this.vy += (dy / distance) * force * 0.01;\n          }\n        }\n\n        // Boundary check\n        if (this.x < 0 || this.x > canvas.width) this.vx *= -1;\n        if (this.y < 0 || this.y > canvas.height) this.vy *= -1;\n\n        // Keep particles in bounds\n        this.x = Math.max(0, Math.min(canvas.width, this.x));\n        this.y = Math.max(0, Math.min(canvas.height, this.y));\n\n        // Fade out as life decreases\n        this.opacity = (this.life / this.maxLife) * 0.5 + 0.2;\n\n        // Reset particle when life ends\n        if (this.life <= 0) {\n          this.x = Math.random() * canvas.width;\n          this.y = Math.random() * canvas.height;\n          this.life = this.maxLife;\n          this.vx = (Math.random() - 0.5) * speed;\n          this.vy = (Math.random() - 0.5) * speed;\n        }\n      }\n\n      draw() {\n        ctx.save();\n        ctx.globalAlpha = this.opacity;\n        \n        // Create gradient for particle\n        const gradient = ctx.createRadialGradient(\n          this.x, this.y, 0,\n          this.x, this.y, this.size\n        );\n        gradient.addColorStop(0, color);\n        gradient.addColorStop(1, 'transparent');\n        \n        ctx.fillStyle = gradient;\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Add glow effect\n        ctx.shadowBlur = 10;\n        ctx.shadowColor = color;\n        ctx.fill();\n        \n        ctx.restore();\n      }\n    }\n\n    // Initialize particles\n    for (let i = 0; i < particleCount; i++) {\n      particles.push(new Particle());\n    }\n\n    particlesRef.current = particles;\n\n    // Mouse move handler\n    const handleMouseMove = (e) => {\n      mouseRef.current.x = e.clientX;\n      mouseRef.current.y = e.clientY;\n    };\n\n    if (interactive) {\n      window.addEventListener('mousemove', handleMouseMove);\n    }\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // Update and draw particles\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n\n      // Draw connections between nearby particles\n      for (let i = 0; i < particles.length; i++) {\n        for (let j = i + 1; j < particles.length; j++) {\n          const dx = particles[i].x - particles[j].x;\n          const dy = particles[i].y - particles[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < 100) {\n            ctx.save();\n            ctx.globalAlpha = (100 - distance) / 100 * 0.2;\n            ctx.strokeStyle = color;\n            ctx.lineWidth = 0.5;\n            ctx.beginPath();\n            ctx.moveTo(particles[i].x, particles[i].y);\n            ctx.lineTo(particles[j].x, particles[j].y);\n            ctx.stroke();\n            ctx.restore();\n          }\n        }\n      }\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (interactive) {\n        window.removeEventListener('mousemove', handleMouseMove);\n      }\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [particleCount, color, size, speed, interactive]);\n\n  return (\n    <motion.div\n      className={`particle-bg ${className}`}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 2 }}\n    >\n      <canvas\n        ref={canvasRef}\n        className=\"absolute inset-0 w-full h-full\"\n        style={{ mixBlendMode: 'screen' }}\n      />\n    </motion.div>\n  );\n};\n\nexport default ParticleField;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,gBAAgB,CAAC,EACrB,gBAAgB,EAAE,EAClB,QAAQ,SAAS,EACjB,OAAO,CAAC,EACR,QAAQ,GAAG,EACX,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC9B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,YAAY,EAAE;YAElB,kBAAkB;YAClB,MAAM;wDAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,UAAU;oBAChC,OAAO,MAAM,GAAG,OAAO,WAAW;gBACpC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,iBAAiB;YACjB,MAAM;gBACJ,aAAc;oBACZ,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;oBACrC,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;oBACtC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAClC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAClC,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,KAAK,OAAO;oBACnC,IAAI,CAAC,OAAO,GAAG,KAAK,MAAM,KAAK,MAAM;oBACrC,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,KAAK,MAAM;oBAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI;gBAC1B;gBAEA,SAAS;oBACP,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;oBACjB,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;oBACjB,IAAI,CAAC,IAAI;oBAET,oBAAoB;oBACpB,IAAI,aAAa;wBACf,MAAM,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;wBACtC,MAAM,KAAK,SAAS,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;wBACtC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;wBAE1C,IAAI,WAAW,KAAK;4BAClB,MAAM,QAAQ,CAAC,MAAM,QAAQ,IAAI;4BACjC,IAAI,CAAC,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;4BACrC,IAAI,CAAC,EAAE,IAAI,AAAC,KAAK,WAAY,QAAQ;wBACvC;oBACF;oBAEA,iBAAiB;oBACjB,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,OAAO,KAAK,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;oBACrD,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,OAAO,MAAM,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;oBAEtD,2BAA2B;oBAC3B,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,IAAI,CAAC,CAAC;oBAClD,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE,IAAI,CAAC,CAAC;oBAEnD,6BAA6B;oBAC7B,IAAI,CAAC,OAAO,GAAG,AAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,GAAI,MAAM;oBAElD,gCAAgC;oBAChC,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG;wBAClB,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;wBACrC,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;wBACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO;wBACxB,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;wBAClC,IAAI,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACpC;gBACF;gBAEA,OAAO;oBACL,IAAI,IAAI;oBACR,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO;oBAE9B,+BAA+B;oBAC/B,MAAM,WAAW,IAAI,oBAAoB,CACvC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GAChB,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI;oBAE3B,SAAS,YAAY,CAAC,GAAG;oBACzB,SAAS,YAAY,CAAC,GAAG;oBAEzB,IAAI,SAAS,GAAG;oBAChB,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;oBAChD,IAAI,IAAI;oBAER,kBAAkB;oBAClB,IAAI,UAAU,GAAG;oBACjB,IAAI,WAAW,GAAG;oBAClB,IAAI,IAAI;oBAER,IAAI,OAAO;gBACb;YACF;YAEA,uBAAuB;YACvB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,UAAU,IAAI,CAAC,IAAI;YACrB;YAEA,aAAa,OAAO,GAAG;YAEvB,qBAAqB;YACrB,MAAM;2DAAkB,CAAC;oBACvB,SAAS,OAAO,CAAC,CAAC,GAAG,EAAE,OAAO;oBAC9B,SAAS,OAAO,CAAC,CAAC,GAAG,EAAE,OAAO;gBAChC;;YAEA,IAAI,aAAa;gBACf,OAAO,gBAAgB,CAAC,aAAa;YACvC;YAEA,iBAAiB;YACjB,MAAM;mDAAU;oBACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C,4BAA4B;oBAC5B,UAAU,OAAO;2DAAC,CAAA;4BAChB,SAAS,MAAM;4BACf,SAAS,IAAI;wBACf;;oBAEA,4CAA4C;oBAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;wBACzC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;4BAC7C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;4BAC1C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;4BAC1C,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;4BAE1C,IAAI,WAAW,KAAK;gCAClB,IAAI,IAAI;gCACR,IAAI,WAAW,GAAG,CAAC,MAAM,QAAQ,IAAI,MAAM;gCAC3C,IAAI,WAAW,GAAG;gCAClB,IAAI,SAAS,GAAG;gCAChB,IAAI,SAAS;gCACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;gCACzC,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;gCACzC,IAAI,MAAM;gCACV,IAAI,OAAO;4BACb;wBACF;oBACF;oBAEA,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;;YAEA;YAEA,UAAU;YACV;2CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,aAAa;wBACf,OAAO,mBAAmB,CAAC,aAAa;oBAC1C;oBACA,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;kCAAG;QAAC;QAAe;QAAO;QAAM;QAAO;KAAY;IAEnD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,YAAY,EAAE,WAAW;QACrC,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAE;kBAE1B,cAAA,6LAAC;YACC,KAAK;YACL,WAAU;YACV,OAAO;gBAAE,cAAc;YAAS;;;;;;;;;;;AAIxC;GA1LM;KAAA;uCA4LS", "debugId": null}}]}